// 使用示例：如何在你的项目中集成标签页隐藏功能

#include "CMvTabWidgetExe.h"
#include "CustomTabBar.h"

// 示例1：使用最简单的方法（推荐用于大多数情况）
void setupTabWidgetSimple()
{
    CMvTabWidgetExe* tabWidget = new CMvTabWidgetExe();
    
    // 添加一些标签页
    tabWidget->addTab(new QWidget(), "Tab 1");
    tabWidget->addTab(new QWidget(), "Tab 2"); 
    tabWidget->addTab(new QWidget(), "Tab 3");
    
    // 设置权限级别信息：格式为 "可见级别-启用级别$-可见级别-启用级别$-..."
    // 例如："5-3$-8-6$-2-1" 表示：
    // Tab 1: 级别5可见，级别3可启用
    // Tab 2: 级别8可见，级别6可启用  
    // Tab 3: 级别2可见，级别1可启用
    tabWidget->setProperty("allTabLevelInfo", "5-3$-8-6$-2-1");
    
    // 设置当前用户级别为4
    tabWidget->handleLevelSimple(4);
    // 结果：Tab 1可见但禁用，Tab 2可见且启用，Tab 3隐藏
}

// 示例2：使用自定义TabBar完全隐藏标签头
void setupTabWidgetCustom()
{
    CMvTabWidgetExe* tabWidget = new CMvTabWidgetExe();
    
    // 设置自定义TabBar
    CustomTabBar* customTabBar = new CustomTabBar();
    tabWidget->setTabBar(customTabBar);
    
    // 添加标签页
    tabWidget->addTab(new QWidget(), "Tab 1");
    tabWidget->addTab(new QWidget(), "Tab 2");
    tabWidget->addTab(new QWidget(), "Tab 3");
    
    // 设置权限信息
    tabWidget->setProperty("allTabLevelInfo", "5-3$-8-6$-2-1");
    
    // 使用自定义TabBar方法
    tabWidget->handleLevelCustomTabBar(4);
}

// 示例3：使用样式表方法
void setupTabWidgetStyleSheet()
{
    CMvTabWidgetExe* tabWidget = new CMvTabWidgetExe();
    
    // 添加标签页
    tabWidget->addTab(new QWidget(), "Tab 1");
    tabWidget->addTab(new QWidget(), "Tab 2");
    tabWidget->addTab(new QWidget(), "Tab 3");
    
    // 设置权限信息
    tabWidget->setProperty("allTabLevelInfo", "5-3$-8-6$-2-1");
    
    // 使用样式表方法
    tabWidget->handleLevelStyleSheet(4);
}

// 示例4：动态权限级别变化
void setupDynamicPermissions()
{
    CMvTabWidgetExe* tabWidget = new CMvTabWidgetExe();
    
    // 添加标签页
    tabWidget->addTab(new QWidget(), "基础功能");    // 级别1可见
    tabWidget->addTab(new QWidget(), "高级功能");    // 级别5可见
    tabWidget->addTab(new QWidget(), "管理员功能");  // 级别10可见
    
    // 设置权限信息
    tabWidget->setProperty("allTabLevelInfo", "1-1$-5-5$-10-10");
    
    // 模拟不同用户级别
    int userLevel = 1;  // 普通用户
    tabWidget->handleLevelSimple(userLevel);
    // 结果：只有"基础功能"可见
    
    userLevel = 5;  // 高级用户
    tabWidget->handleLevelSimple(userLevel);
    // 结果："基础功能"和"高级功能"可见
    
    userLevel = 10; // 管理员
    tabWidget->handleLevelSimple(userLevel);
    // 结果：所有标签页都可见
}

// 在你的CMvTabWidgetExe类中，你需要添加这些方法声明到头文件：
/*
class CMvTabWidgetExe : public QTabWidget
{
    Q_OBJECT
    
public:
    // 原有的构造函数和方法...
    
    // 新增的权限控制方法
    void handleLevel(int level);                    // 方案1：移除/插入
    void handleLevelStyleSheet(int level);          // 方案2：样式表
    void handleLevelCustomTabBar(int level);        // 方案3：自定义TabBar
    void handleLevelSimple(int level);              // 方案4：简单方法（推荐）
    
private:
    // 原有的成员变量...
};
*/
